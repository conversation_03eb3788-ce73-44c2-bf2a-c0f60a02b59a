import { create } from 'zustand';
import { ModelProvider, ModelConfig } from './models';
import { getAllModels, getModelsByProvider } from './models';

// 自定义模型接口
export interface CustomModel {
  id: string;
  name: string;
  provider: ModelProvider;
  maxTokens?: number;
  temperature?: number;
}

// AI状态接口
interface AIState {
  provider: ModelProvider;
  model: string;
  customModels: CustomModel[];
  apiKeys: Record<ModelProvider, string>;
  baseUrls: Record<ModelProvider, string>;
  setProvider: (provider: ModelProvider) => void;
  setModel: (model: string) => void;
  setCustomModels: (models: CustomModel[]) => void;
  setApiKey: (provider: ModelProvider, apiKey: string) => void;
  setBaseUrl: (provider: ModelProvider, baseUrl: string) => void;
  reset: () => void;
}

// 默认状态
const DEFAULT_STATE = {
  provider: 'openai' as ModelProvider,
  model: 'gpt-4o',
  customModels: [] as CustomModel[],
  apiKeys: {
    openai: '',
    xai: '',
    deepseek: '',
    anthropic: ''
  },
  baseUrls: {
    openai: '',
    xai: '',
    deepseek: '',
    anthropic: ''
  }
};

/**
 * AI状态管理存储
 * 使用Zustand管理AI提供商和模型的状态
 */
export const useAIStore = create<AIState>((set) => ({
  // 初始状态
  provider: DEFAULT_STATE.provider,
  model: DEFAULT_STATE.model,
  customModels: DEFAULT_STATE.customModels,
  apiKeys: DEFAULT_STATE.apiKeys,
  baseUrls: DEFAULT_STATE.baseUrls,

  // 设置提供商
  setProvider: (provider: ModelProvider) => set((state) => {
    // 获取新提供商的模型列表
    const defaultModels = getModelsByProvider(provider);

    // 检查当前模型是否在新提供商的模型列表中
    const modelExists = defaultModels.some(m => m.id === state.model) ||
                       state.customModels.some(m => m.id === state.model && m.provider === provider);

    // 如果当前模型不在新提供商的模型列表中，选择第一个可用模型
    const newModel = modelExists ? state.model : (defaultModels[0]?.id || '');

    return {
      provider,
      model: newModel,
    };
  }),

  // 设置模型
  setModel: (model: string) => set({ model }),

  // 设置自定义模型
  setCustomModels: (customModels: CustomModel[]) => set({ customModels }),

  // 设置API密钥
  setApiKey: (provider: ModelProvider, apiKey: string) => set((state) => ({
    apiKeys: {
      ...state.apiKeys,
      [provider]: apiKey
    }
  })),

  // 设置基础URL
  setBaseUrl: (provider: ModelProvider, baseUrl: string) => set((state) => ({
    baseUrls: {
      ...state.baseUrls,
      [provider]: baseUrl
    }
  })),

  // 重置状态
  reset: () => set(DEFAULT_STATE),
}));

/**
 * 获取当前提供商的所有模型列表（包括自定义模型）
 */
export function getAllModelsForProvider(provider: ModelProvider, customModels: CustomModel[] = []): (ModelConfig | CustomModel)[] {
  const defaultModels = getModelsByProvider(provider);
  const providerCustomModels = customModels.filter(m => m.provider === provider);
  return [...defaultModels, ...providerCustomModels];
}

/**
 * AI配置管理器
 * 用于保存和加载AI配置
 */
export class AIConfigManager {
  private cookies: any;

  constructor(cookies: any) {
    this.cookies = cookies;
  }

  /**
   * 获取配置
   */
  async getConfig() {
    const config = {
      provider: this.cookies.get('ai_provider')?.value || DEFAULT_STATE.provider,
      model: this.cookies.get('ai_model')?.value || DEFAULT_STATE.model,
      apiKeys: {
        openai: this.cookies.get('ai_api_key_openai')?.value || '',
        xai: this.cookies.get('ai_api_key_xai')?.value || '',
        deepseek: this.cookies.get('ai_api_key_deepseek')?.value || '',
        anthropic: this.cookies.get('ai_api_key_anthropic')?.value || ''
      },
      baseUrls: {
        openai: this.cookies.get('ai_base_url_openai')?.value || '',
        xai: this.cookies.get('ai_base_url_xai')?.value || '',
        deepseek: this.cookies.get('ai_base_url_deepseek')?.value || '',
        anthropic: this.cookies.get('ai_base_url_anthropic')?.value || ''
      },
      customModels: []
    };

    // 获取自定义模型
    try {
      const customModelsJson = this.cookies.get('ai_custom_models')?.value;
      if (customModelsJson) {
        config.customModels = JSON.parse(customModelsJson);
      }
    } catch (error) {
      console.error('解析自定义模型失败:', error);
    }

    return config;
  }

  /**
   * 保存配置
   */
  async saveConfig(config: {
    provider: ModelProvider;
    model: string;
    apiKeys: Record<ModelProvider, string>;
    baseUrls: Record<ModelProvider, string>;
    customModels: CustomModel[];
  }) {
    // 保存基本配置
    this.cookies.set('ai_provider', config.provider);
    this.cookies.set('ai_model', config.model);

    // 保存API密钥
    Object.entries(config.apiKeys).forEach(([provider, apiKey]) => {
      this.cookies.set(`ai_api_key_${provider}`, apiKey);
    });

    // 保存基础URL
    Object.entries(config.baseUrls).forEach(([provider, baseUrl]) => {
      this.cookies.set(`ai_base_url_${provider}`, baseUrl);
    });

    // 保存自定义模型
    if (config.customModels && config.customModels.length > 0) {
      this.cookies.set('ai_custom_models', JSON.stringify(config.customModels));
    } else {
      this.cookies.set('ai_custom_models', '');
    }
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { createAIClient, Message } from '@/lib/ai-client';
import { ModelProvider, getModelById } from '@/lib/models';
import { createDefaultPrompt, createPresentationPrompt, createDataVisualizationPrompt } from '@/lib/prompt-modules';
import { AIConfigManager } from '@/lib/ai-store';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { messages, model } = body;
    // 原始消息，可能包含 'task' 角色
    const rawMessages = messages as Array<{ role: string; content: string }>;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: 'Invalid messages format' },
        { status: 400 }
      );
    }

    // 在没有 system 消息时，前置系统提示
    if (!rawMessages.some(msg => msg.role === 'system')) {
      // 根据请求中的任务类型选择适当的提示词
      let systemPrompt = createPresentationPrompt();
      console.log('使用系统提示:', systemPrompt);

      // 检查请求中是否包含任务类型信息
      // const taskType = request.headers.get('x-task-type');
      // if (taskType) {
      //   switch (taskType) {
      //     case 'presentation':
      //       systemPrompt = createPresentationPrompt();
      //       break;
      //     case 'data-visualization':
      //       systemPrompt = createDataVisualizationPrompt();
      //       break;
      //     // 可以根据需要添加更多任务类型
      //   }
      // }

      rawMessages.unshift({
        role: 'system',
        content: systemPrompt
      });
    }

    // 为API准备消息：将自定义 'task' 角色映射为 'user'
    const apiMessages: Message[] = rawMessages.map(msg => ({
      role: msg.role === 'task' ? 'user' : (msg.role as 'user' | 'assistant' | 'system'),
      content: msg.content,
    }));

    // 过滤掉空内容消息，避免 API 报错
    const filteredMessages = apiMessages.filter(m => m.content?.trim());

    // 获取API密钥和基础URL
    const providerCookie = request.cookies.get('ai_provider');
    const modelCookie = request.cookies.get('ai_model');

    // 获取模型配置
    const cookieStore = cookies();
    const configManager = new AIConfigManager(cookieStore);
    const { customModels } = await configManager.getConfig();
    const modelConfig = getModelById(model) || customModels.find((m: any) => m.id === model);

    // 根据模型配置或模型名称确定提供商
    let provider: ModelProvider;

    console.log('模型名称:', model);
    console.log('找到的模型配置:', modelConfig);

    if (modelConfig && 'provider' in modelConfig) {
      // 如果找到模型配置，使用配置中的提供商
      provider = modelConfig.provider as ModelProvider;
      console.log('使用模型配置中的提供商:', provider);
    } else if (model.startsWith('gpt-') || model === 'gpt-4o') {
      provider = 'openai';
    } else if (model.startsWith('grok-')) {
      provider = 'xai';
    } else if (model === 'deepseek-coder' || model.startsWith('deepseek-')) {
      provider = 'deepseek';
    } else {
      // 默认提供商
      provider = 'openai';
    }

    console.log('识别的提供商:', provider);

    // 获取特定提供商的API密钥和基础URL
    const apiKeyCookie = request.cookies.get(`ai_api_key_${provider}`);
    const baseUrlCookie = request.cookies.get(`ai_base_url_${provider}`);
    const apiKey = apiKeyCookie?.value || '';
    const baseUrl = baseUrlCookie?.value || '';

    // 添加调试日志
    console.log('API调用配置:', {
      model,
      provider,
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length || 0,
      hasBaseUrl: !!baseUrl,
      baseUrlLength: baseUrl?.length || 0,
      cookieName: `ai_api_key_${provider}`
    });

    // 创建API密钥和基础URL对象
    const apiKeys: Partial<Record<ModelProvider, string>> = {};
    apiKeys[provider as ModelProvider] = apiKey;

    const baseUrls: Partial<Record<ModelProvider, string>> = {};
    if (baseUrl) {
      baseUrls[provider as ModelProvider] = baseUrl;
    }

    // 创建AI客户端
    const aiClient = createAIClient({
      apiKeys: apiKeys as Record<ModelProvider, string>,
      baseUrls: baseUrls as Record<ModelProvider, string>,
      customModels: customModels
    });

    // 调用AI客户端
    let response = await aiClient.chatCompletion({
      model,
      messages: filteredMessages,
    });

    let fullContent = response.content;
    let attempts = 0;
    const maxAttempts = 3; // 最多续写3次

    // 检测是否需要续写
    while (attempts < maxAttempts) {
      // 检测是否因为token限制而截断
      const currentModelConfig = getModelById(model) || customModels.find((m: any) => m.id === model);
      const isTokenLimitReached = response.usage &&
        response.usage.completionTokens >= (currentModelConfig?.maxTokens ?? 4096) * 0.95;

      // 检测内容是否不完整（简单启发式检测）
      const hasIncompleteMarkers = fullContent.includes('```') &&
        !fullContent.match(/```[\s\S]*?```$/);

      const isIncomplete = isTokenLimitReached || hasIncompleteMarkers;

      if (!isIncomplete) {
        break; // 内容完整，退出循环
      }

      console.log(`检测到内容不完整，进行第${attempts + 1}次续写...`);

      try {
        // 构建续写消息
        const continueMessages = [
          ...filteredMessages,
          { role: 'assistant' as const, content: fullContent },
          { role: 'user' as const, content: '请继续完成上述内容，从中断的地方继续。重要要求：\n1. 不要重复已有内容\n2. 不要添加新的代码块标记（```）\n3. 直接从中断处继续输出原始内容\n4. 保持与前面内容的连贯性' }
        ];

        // 调用续写
        const continueResponse = await aiClient.chatCompletion({
          model,
          messages: continueMessages,
        });

        // 清理并合并内容
        let cleanContinueContent = continueResponse.content;

        // 移除各种可能的代码块标记
        cleanContinueContent = cleanContinueContent.replace(/^```html\{filename=[^}]+\}\s*/g, '');
        cleanContinueContent = cleanContinueContent.replace(/^```html\s*/g, '');
        cleanContinueContent = cleanContinueContent.replace(/^```markdown\{filename=[^}]+\}\s*/g, '');
        cleanContinueContent = cleanContinueContent.replace(/^```markdown\s*/g, '');
        cleanContinueContent = cleanContinueContent.replace(/^```md\s*/g, '');
        cleanContinueContent = cleanContinueContent.replace(/^```\s*/g, '');

        // 移除结尾的代码块标记
        cleanContinueContent = cleanContinueContent.replace(/\s*```\s*$/g, '');

        // 移除中间可能出现的代码块标记
        cleanContinueContent = cleanContinueContent.replace(/\n```html\{filename=[^}]+\}\s*/g, '\n');
        cleanContinueContent = cleanContinueContent.replace(/\n```html\s*/g, '\n');
        cleanContinueContent = cleanContinueContent.replace(/\n```markdown\{filename=[^}]+\}\s*/g, '\n');
        cleanContinueContent = cleanContinueContent.replace(/\n```markdown\s*/g, '\n');
        cleanContinueContent = cleanContinueContent.replace(/\n```md\s*/g, '\n');

        // 合并内容（不添加额外的换行，直接连接）
        fullContent += cleanContinueContent;
        response = continueResponse; // 更新response以获取最新的usage信息
        attempts++;

        console.log(`第${attempts}次续写完成，内容长度: ${fullContent.length}`);
      } catch (continueError) {
        console.error('续写失败:', continueError);
        fullContent += '\n\n[续写失败，内容可能不完整]';
        break;
      }
    }

    if (attempts >= maxAttempts) {
      fullContent += '\n\n[已达到最大续写次数，内容可能仍不完整]';
    }

    return NextResponse.json({
      message: {
        role: 'assistant',
        content: fullContent,
        timestamp: Date.now(),
      },
      model: response.model,
      continuationAttempts: attempts, // 返回续写次数信息
    });
  } catch (error) {
    console.error('Error in chat API:', error);

    // 返回备用响应
    return NextResponse.json({
      message: {
        role: 'assistant',
        content: '抱歉，我暂时无法处理您的请求。请稍后再试或检查您的API配置。',
        timestamp: Date.now(),
      },
      model: 'fallback',
    });
  }
}
